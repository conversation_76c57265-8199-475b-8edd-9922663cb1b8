name: Siflowtype
options:
  bundleIdPrefix: com.siflowtype.inputmethod
packages:
  OpenAI:
    url: https://github.com/MacPaw/OpenAI.git
    branch: main
targets:
  Siflowtype:
    type: application
    platform: macOS
    deploymentTarget: "12.0"
    sources:
      - Application
      - Features
      - Services
      - Utilities
    resources:
      - Resources/Assets.xcassets
      - Resources/MenuIcon.icns
      - Resources/Localizable.xcstrings
    info:
      path: Resources/Info.plist
      properties:
        LSApplicationCategoryType: public.app-category.utilities
    entitlements:
      path: Resources/Siflowtype.entitlements
    dependencies:
      - package: OpenAI
    settings:
      base:
        MARKETING_VERSION: 0.1.0
        CURRENT_PROJECT_VERSION: 12
        MACOSX_DEPLOYMENT_TARGET: "12.0"
        ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
        CODE_SIGN_STYLE: Automatic
        DEVELOPMENT_TEAM: 4MQ3AWP5NR
  SiflowtypeTests:
    type: bundle.unit-test
    platform: macOS
    sources:
      - Tests
    dependencies:
      - target: Siflowtype
      - package: OpenAI
    settings:
      base:
        MACOSX_DEPLOYMENT_TARGET: "12.0"