name: Siflowtype
options:
  bundleIdPrefix: com.siflowtype.inputmethod
packages:
  OpenAI:
    url: https://github.com/MacPaw/OpenAI.git
    branch: main
targets:
  Siflowtype:
    type: application
    platform: macOS
    deploymentTarget: "12.0"
    sources:
      - Application
      - Features
      - Services
      - Utilities
    resources:
      - Resources/Assets.xcassets
      - Resources/MenuIcon.icns
      - Resources/Localizable.xcstrings
    info:
      path: Resources/Info.plist
      properties:
        CFBundleDevelopmentRegion: en
        CFBundleDisplayName: Siflowtype
        CFBundleLocalizations:
          - en
          - zh-Hans
        CFBundleName: Siflowtype
        CFBundlePackageType: APPL
        ComponentInputModeDict:
          tsInputModeListKey:
            com.siflowtype.inputmethod.Siflowtype.English:
              TISInputSourceID: com.siflowtype.inputmethod.Siflowtype.English
              TISIntendedLanguage: en-US
              tsInputModeAlternateMenuIconFileKey: siflowtype.pdf
              tsInputModeCharacterRepertoireKey:
                - English
              tsInputModeDefaultStateKey: true
              tsInputModeIsVisibleKey: true
              tsInputModeKeyEquivalentModifiersKey: 4608
              tsInputModeMenuIconFileKey: siflowtype.pdf
              tsInputModePaletteIconFileKey: siflowtype.pdf
              tsInputModePrimaryInScriptKey: true
              tsInputModeScriptKey: smUnicodeScript
          tsVisibleInputModeOrderedArrayKey:
            - com.siflowtype.inputmethod.Siflowtype.English
        InputMethodConnectionName: Siflowtype_Connection
        InputMethodServerControllerClass: Siflowtype.SiflowtypeInputController
        InputMethodServerDelegateClass: Siflowtype.SiflowtypeInputController
        LSApplicationCategoryType: public.app-category.utilities
        LSBackgroundOnly: false
        LSUIElement: true
        NSPrincipalClass: NSApplication
        TICapsLockLanguageSwitchCapable: true
    entitlements:
      path: Resources/Siflowtype.entitlements
    dependencies:
      - package: OpenAI
    settings:
      base:
        MARKETING_VERSION: 0.1.0
        CURRENT_PROJECT_VERSION: 12
        MACOSX_DEPLOYMENT_TARGET: "12.0"
        ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
        CODE_SIGN_STYLE: Automatic
        DEVELOPMENT_TEAM: 4MQ3AWP5NR
  SiflowtypeTests:
    type: bundle.unit-test
    platform: macOS
    sources:
      - Tests
    dependencies:
      - target: Siflowtype
      - package: OpenAI
    settings:
      base:
        MACOSX_DEPLOYMENT_TARGET: "12.0"