//
//  OpenAIGeneratorE2ETests.swift
//  SiflowtypeTests
//
//  Created by SwiftCoder on 2024/12/19.
//

import XCTest
import Foundation
import OpenAI
@testable import Siflowtype


/// OpenAIGenerator 端到端测试套件
///
/// 这个测试套件验证 OpenAIGenerator 在真实开发环境配置下的完整功能流程，
/// 包括网络请求、响应解析、错误处理和重试机制。
class OpenAIGeneratorE2ETests: XCTestCase {
    
    // MARK: - 测试配置
    
    /// 测试用的开发环境配置
    private let testConfig = DevelopmentEnvironment()
    
    /// 测试用的 OpenAIGenerator 实例
    private var generator: OpenAIGenerator {
        OpenAIGenerator(config: testConfig)
    }
    
    /// 测试用的提示词配置
    private var promptConfig: PromptConfiguration {
        return PromptConfiguration(
            systemPrompt: """
            你是一个专业的英文写作助手。用户会给你一段英文文本，你需要提供3个改进建议。
            每个建议都应该包含：
            1. completed: 改进后的完整文本
            2. explanation: 简短的中文解释说明改进的原因
            
            请确保建议具有实用性和多样性。
            """,
            userPromptTemplate: """
            请为以下英文文本提供{suggestionCount}个改进建议：
            
            原文：{user_input}
            
            请返回JSON格式的响应。
            """,
            model: "google/gemini-flash-1.5-8b",
            maxInputLength: 500,
            sourceLanguage: "en",
            targetLanguage: "zh",
            suggestionCount: 3
        )
    }
    
    // MARK: - 成功场景测试

    /// 测试基本的文本生成功能
    func testBasicTextGeneration() async throws {
        let inputText = "I want to improve my writing skills."
        
        let result = try await generator.generate(
            for: inputText,
            with: promptConfig,
            temperature: 0.2,
            timeout: 30.0,
            retries: 2
        )
        
        // 验证响应结构
        XCTAssertTrue(result.suggestions.count > 0, "应该返回至少一个建议")
        XCTAssertTrue(result.suggestions.count <= 3, "建议数量不应超过配置的最大值")

        // 验证每个建议的内容
        for suggestion in result.suggestions {
            XCTAssertFalse(suggestion.completed.isEmpty, "completed 字段不应为空")
            XCTAssertFalse(suggestion.explanation.isEmpty, "explanation 字段不应为空")
            XCTAssertNotEqual(suggestion.completed, inputText, "改进后的文本应该与原文不同")
        }
    }
    
    /// 测试使用默认参数的便利方法
    func testGenerationWithDefaults() async throws {
        let inputText = "This is a simple test sentence."
        
        let result = try await generator.generateWithDefaults(
            for: inputText,
            with: promptConfig
        )
        
        XCTAssertTrue(result.suggestions.count > 0, "应该返回建议")

        // 验证建议质量
        let firstSuggestion = result.suggestions[0]
        XCTAssertTrue(firstSuggestion.completed.count > inputText.count / 2, "改进后的文本应该有合理的长度")
    }
    
    /// 测试较长文本的处理
    func testLongTextProcessing() async throws {
        let longText = """
        The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet. 
        It is commonly used for testing purposes in typography and font design. The sentence has been 
        used since the early 20th century and remains popular today.
        """
        
        let result = try await generator.generate(
            for: longText,
            with: promptConfig,
            temperature: 0.1,
            timeout: 45.0,
            retries: 1
        )
        
        XCTAssertTrue(result.suggestions.count > 0, "应该能处理较长的文本")

        // 验证改进建议的多样性
        let suggestions = result.suggestions.map { $0.completed }
        let uniqueSuggestions = Set(suggestions)
        XCTAssertEqual(uniqueSuggestions.count, suggestions.count, "建议应该是唯一的")
    }
    
    // MARK: - 错误处理测试

    /// 测试输入验证失败的情况
    func testInputValidationFailure() async {
        let emptyInput = ""

        do {
            _ = try await generator.generate(
                for: emptyInput,
                with: promptConfig
            )
            XCTFail("应该抛出 GeneratorError")
        } catch {
            XCTAssertTrue(error is GeneratorError, "应该抛出 GeneratorError 类型的错误")
        }
    }
    
    /// 测试超长输入的处理
    func testOversizedInputHandling() async {
        // 创建一个超过最大长度限制的输入
        let oversizedInput = String(repeating: "This is a very long sentence. ", count: 20)

        do {
            _ = try await generator.generate(
                for: oversizedInput,
                with: promptConfig
            )
            XCTFail("应该抛出 GeneratorError")
        } catch {
            XCTAssertTrue(error is GeneratorError, "应该抛出 GeneratorError 类型的错误")
        }
    }
    
    /// 测试网络超时处理
    func testNetworkTimeoutHandling() async {
        let inputText = "Test timeout scenario."

        // 使用极短的超时时间来模拟超时
        do {
            _ = try await generator.generate(
                for: inputText,
                with: promptConfig,
                temperature: 0.2,
                timeout: 0.001, // 极短的超时时间
                retries: 0
            )
            XCTFail("应该抛出 GeneratorError")
        } catch {
            XCTAssertTrue(error is GeneratorError, "应该抛出 GeneratorError 类型的错误")
        }
    }
    
    // MARK: - 重试机制测试

    /// 测试重试机制的基本功能
    func testRetryMechanism() async {
        let inputText = "Test retry mechanism."
        
        // 使用较短的超时时间，但允许重试
        let result = try? await generator.generate(
            for: inputText,
            with: promptConfig,
            temperature: 0.2,
            timeout: 10.0,
            retries: 2
        )
        
        // 即使有重试，最终应该能成功或抛出明确的错误
        if let result = result {
            XCTAssertTrue(result.suggestions.count > 0, "重试后应该能获得结果")
        }
        // 如果失败，应该是因为网络问题而不是代码错误
    }
    
    // MARK: - 配置测试

    /// 测试不同温度参数的影响
    func testDifferentTemperatureParameters() async throws {
        let inputText = "Creative writing test."
        
        // 测试低温度（更确定性）
        let lowTempResult = try await generator.generate(
            for: inputText,
            with: promptConfig,
            temperature: 0.1,
            timeout: 30.0,
            retries: 1
        )
        
        // 测试高温度（更创造性）
        let highTempResult = try await generator.generate(
            for: inputText,
            with: promptConfig,
            temperature: 0.8,
            timeout: 30.0,
            retries: 1
        )
        
        XCTAssertTrue(lowTempResult.suggestions.count > 0, "低温度应该产生结果")
        XCTAssertTrue(highTempResult.suggestions.count > 0, "高温度应该产生结果")
        
        // 验证两次结果可能不同（由于温度差异）
        let lowTempTexts = lowTempResult.suggestions.map { $0.completed }
        let highTempTexts = highTempResult.suggestions.map { $0.completed }
        
        // 注意：由于随机性，这个测试可能偶尔失败，但大多数情况下应该通过
        let hasAnyDifference = !Set(lowTempTexts).isDisjoint(with: Set(highTempTexts))
        // 我们不强制要求结果必须不同，因为这取决于模型的行为
    }
    
    // MARK: - 性能测试

    /// 测试并发请求处理
    func testConcurrentRequestHandling() async throws {
        let inputTexts = [
            "First test sentence.",
            "Second test sentence.",
            "Third test sentence."
        ]
        
        // 并发执行多个请求
        let results = try await withThrowingTaskGroup(of: CandidateResponse.self) { group in
            for text in inputTexts {
                group.addTask {
                    try await self.generator.generate(
                        for: text,
                        with: self.promptConfig,
                        temperature: 0.2,
                        timeout: 30.0,
                        retries: 1
                    )
                }
            }
            
            var collectedResults: [CandidateResponse] = []
            for try await result in group {
                collectedResults.append(result)
            }
            return collectedResults
        }
        
        XCTAssertEqual(results.count, inputTexts.count, "应该返回所有请求的结果")

        for result in results {
            XCTAssertTrue(result.suggestions.count > 0, "每个结果都应该包含建议")
        }
    }
    
    // MARK: - 集成测试

    /// 测试与真实 API 的完整集成
    func testFullAPIIntegration() async throws {
        let testScenarios = [
            "Hello world",
            "I need help with my homework.",
            "The weather is nice today.",
            "Can you explain quantum physics?"
        ]
        
        for scenario in testScenarios {
            let result = try await generator.generateWithDefaults(
                for: scenario,
                with: promptConfig
            )
            
            XCTAssertTrue(result.suggestions.count > 0, "场景 '\(scenario)' 应该产生建议")

            // 验证响应质量
            for suggestion in result.suggestions {
                XCTAssertTrue(suggestion.completed.count > 0, "建议文本不应为空")
                XCTAssertTrue(suggestion.explanation.count > 0, "解释不应为空")

                // 验证建议文本与原文不完全相同
                XCTAssertNotEqual(suggestion.completed.lowercased(), scenario.lowercased(),
                       "建议应该与原文有所不同")
            }
        }
    }
    
    // MARK: - 边界条件测试

    /// 测试特殊字符和 Unicode 处理
    func testSpecialCharactersHandling() async throws {
        let specialTexts = [
            "Hello 世界! 🌍",
            "Café résumé naïve",
            "Testing @#$%^&*() symbols",
            "Line\nbreak\ttab test"
        ]
        
        for text in specialTexts {
            let result = try await generator.generate(
                for: text,
                with: promptConfig,
                temperature: 0.2,
                timeout: 30.0,
                retries: 1
            )
            
            XCTAssertTrue(result.suggestions.count > 0, "特殊字符文本 '\(text)' 应该被正确处理")
        }
    }
}

// MARK: - 测试辅助扩展

extension GeneratorError {
    /// 检查错误是否为预期的类型
    var isExpectedTestError: Bool {
        switch self {
        case .requestTimedOut, .inputValidationFailed, .networkError:
            return true
        default:
            return false
        }
    }
}
